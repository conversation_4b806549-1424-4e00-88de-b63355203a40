//
//  DesignSystem.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

// MARK: - Design System

/// Centralized design system for J&T YOU app
/// Provides consistent colors, typography, spacing, and visual effects
struct DesignSystem {

    // MARK: - Colors

    struct Colors {
        // MARK: - Brand Colors
        static let primaryTeal = Color(red: 0.01, green: 0.65, blue: 0.57)
        static let primaryTealLight = Color(red: 0.2, green: 0.8, blue: 0.7)
        static let primaryTealDark = Color(red: 0, green: 0.57, blue: 0.51)

        // MARK: - Background Colors
        static let backgroundPrimary = Color.black
        static let backgroundGradientTop = Color(red: 0.004, green: 0.133, blue: 0.122) // #01221F
        static let backgroundGradientBottom = Color.black

        // MARK: - Surface Colors
        static let surfacePrimary = Color(red: 0.004, green: 0.133, blue: 0.122) // #01221F
        static let surfaceSecondary = Color.white.opacity(0.1)

        // MARK: - Text Colors
        static let textPrimary = Color.white
        static let textSecondary = Color.white.opacity(0.7)
        static let textTertiary = Color.white.opacity(0.6)
        static let textInverse = Color.black

        // MARK: - Border Colors
        static let borderPrimary = Color.white.opacity(0.25)
        static let borderSecondary = Color.white.opacity(0.5)

        // MARK: - Shadow Colors
        static let shadowPrimary = Color.white.opacity(0.06)
        static let shadowSecondary = Color.black.opacity(0.40)
    }

    // MARK: - Typography

    struct Typography {
        // MARK: - Font Family
        static let fontFamily = "SF Pro"

        // MARK: - Font Sizes
        static let largeTitle: CGFloat = 48
        static let title1: CGFloat = 28
        static let title2: CGFloat = 22
        static let title3: CGFloat = 20
        static let headline: CGFloat = 17
        static let body: CGFloat = 16
        static let callout: CGFloat = 16
        static let subheadline: CGFloat = 15
        static let footnote: CGFloat = 13
        static let caption1: CGFloat = 12
        static let caption2: CGFloat = 11

        // MARK: - Font Styles
        static let largeTitleFont = Font.custom(fontFamily, size: Typography.largeTitle).weight(.bold)
        static let title1Font = Font.custom(fontFamily, size: Typography.title1).weight(.bold)
        static let title2Font = Font.custom(fontFamily, size: Typography.title2).weight(.bold)
        static let title3Font = Font.custom(fontFamily, size: Typography.title3).weight(.semibold)
        static let headlineFont = Font.custom(fontFamily, size: Typography.headline).weight(.semibold)
        static let bodyFont = Font.custom(fontFamily, size: Typography.body).weight(.regular)
        static let bodyMediumFont = Font.custom(fontFamily, size: Typography.body).weight(.medium)
        static let calloutFont = Font.custom(fontFamily, size: Typography.callout).weight(.regular)
        static let subheadlineFont = Font.custom(fontFamily, size: Typography.subheadline).weight(.regular)
        static let footnoteFont = Font.custom(fontFamily, size: Typography.footnote).weight(.regular)
        static let caption1Font = Font.custom(fontFamily, size: Typography.caption1).weight(.regular)
        static let caption2Font = Font.custom(fontFamily, size: Typography.caption2).weight(.regular)
    }

    // MARK: - Spacing

    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let xxxl: CGFloat = 32
        static let xxxxl: CGFloat = 40
        static let xxxxxl: CGFloat = 48

        // MARK: - Semantic Spacing
        static let componentPadding = lg
        static let sectionSpacing = xxl
        static let screenPadding = lg
        static let buttonPadding = md
        static let inputPadding = lg
    }

    // MARK: - Corner Radius

    struct CornerRadius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 18
        static let xxl: CGFloat = 25
        static let round: CGFloat = 50

        // MARK: - Semantic Corner Radius
        static let button = xxl
        static let card = md
        static let input = md
        static let messageBubble = xl
        static let iPhoneBezel: CGFloat = 39
    }

    // MARK: - Dimensions

    struct Dimensions {
        // MARK: - Button Sizes
        static let buttonHeight: CGFloat = 44
        static let smallButtonSize: CGFloat = 36
        static let iconButtonSize: CGFloat = 36

        // MARK: - Input Sizes
        static let inputMinHeight: CGFloat = 44

        // MARK: - Orb Sizes
        static let orbSize: CGFloat = 200
        static let orbGlowSize: CGFloat = 200

        // MARK: - Chat
        static let messageBubbleMaxWidth: CGFloat = 280

        // MARK: - Home Indicator (for custom styling only)
        static let homeIndicatorWidth: CGFloat = 134
        static let homeIndicatorThickness: CGFloat = 5
        static let homeIndicatorHeight: CGFloat = 34
    }

    // MARK: - Animation

    struct Animation {
        // MARK: - Durations
        static let fast: Double = 0.3
        static let medium: Double = 0.5
        static let slow: Double = 0.8
        static let extraSlow: Double = 1.0

        // MARK: - Spring Animations
        static let springFast = SwiftUI.Animation.spring(response: 0.6, dampingFraction: 0.8)
        static let springMedium = SwiftUI.Animation.spring(response: 0.8, dampingFraction: 0.8)
        static let springSlow = SwiftUI.Animation.spring(response: 1.0, dampingFraction: 0.8)

        // MARK: - Easing Animations
        static let easeInOut = SwiftUI.Animation.easeInOut(duration: medium)
        static let easeInOutFast = SwiftUI.Animation.easeInOut(duration: fast)
        static let easeInOutSlow = SwiftUI.Animation.easeInOut(duration: slow)

        // MARK: - Orb Animations
        static let orbPulse = SwiftUI.Animation.easeInOut(duration: 2.0).repeatForever(autoreverses: true)
        static let orbScale = SwiftUI.Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true)

        // MARK: - Typing Animation
        static let typingDot = SwiftUI.Animation.easeInOut(duration: 0.6).repeatForever()
    }

    // MARK: - Shadows

    struct Shadows {
        static let small = Shadow(color: Colors.shadowPrimary, radius: 4, x: 0, y: 2)
        static let medium = Shadow(color: Colors.shadowPrimary, radius: 8, x: 0, y: 4)
        static let large = Shadow(color: Colors.shadowPrimary, radius: 16, x: 0, y: 8)
        static let orb = Shadow(color: Colors.shadowPrimary, radius: 36, x: 0, y: 20)
    }

    // MARK: - Blur Effects

    struct BlurEffects {
        static let thin: Material = .thinMaterial
        static let regular: Material = .regularMaterial
        static let thick: Material = .thickMaterial
        static let ultraThin: Material = .ultraThinMaterial
    }
}

// MARK: - Shadow Helper

struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Extensions

extension View {
    /// Apply a design system shadow
    func designSystemShadow(_ shadow: Shadow) -> some View {
        self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }

    /// Apply safe area aware padding for consistent spacing across devices
    func safeAreaAwarePadding(_ edges: Edge.Set = .all, _ length: CGFloat? = nil) -> some View {
        self.padding(edges, length)
    }

    /// Ignore safe area with explicit edge specification for clarity
    func ignoreAllSafeAreas() -> some View {
        self.ignoresSafeArea(.all, edges: .all)
    }

    /// Ignore safe area only for container, respecting keyboard safe area
    func ignoreContainerSafeArea() -> some View {
        self.ignoresSafeArea(.container, edges: .all)
    }
}
