//
//  ReceiptCardView.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

struct ReceiptCardView: View {
    let receiptData: ReceiptData

    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            VStack(spacing: 24) {
                // Receipt image thumbnail
                Group {
                    if let imageData = receiptData.imageData,
                       let uiImage = UIImage(data: imageData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 160)
                            .clipped()
                            .cornerRadius(8)
                    } else {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 120, height: 160)
                            .background(Color(red: 0.50, green: 0.23, blue: 0.27).opacity(0.50))
                            .cornerRadius(8)
                            .overlay(
                                Image(systemName: "doc.text")
                                    .foregroundColor(.white.opacity(0.6))
                                    .font(.system(size: 24))
                            )
                    }
                }

                VStack(alignment: .trailing, spacing: 8) {
                    // Document Type row
                    receiptRow(
                        icon: .documentType,
                        title: receiptData.documentType,
                        subtitle: "Typ dokumentu"
                    )

                    divider()

                    // Date row
                    receiptRow(
                        icon: .calendar,
                        title: receiptData.date,
                        subtitle: "Datum"
                    )

                    divider()

                    // Supplier row
                    receiptRow(
                        icon: .building,
                        title: receiptData.supplier,
                        subtitle: "Dodavatel"
                    )

                    divider()

                    // Amount row
                    receiptRow(
                        icon: .money,
                        title: receiptData.formattedAmount,
                        subtitle: "Částka"
                    )

                    divider()

                    // Payment Method row
                    receiptRow(
                        icon: .paymentMethod,
                        title: receiptData.paymentMethod == "NOT_EXTRACTED" ? "Nerozpoznáno" : receiptData.paymentMethod,
                        subtitle: "Způsob platby"
                    )

                    divider()

                    // Description row
                    receiptRow(
                        icon: .description,
                        title: receiptData.description.isEmpty ? "Bez popisu" : receiptData.description,
                        subtitle: "Popis"
                    )

                    divider()

                    // Category row
                    receiptRow(
                        icon: .category,
                        title: receiptData.category,
                        subtitle: "Kategorie",
                        showCheckbox: true
                    )
                }
            }
            .padding(16)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .inset(by: 0.50)
                    .stroke(Color(red: 0, green: 0.20, blue: 0.18), lineWidth: 0.50)
            )
        }
    }

    private func receiptRow(
        icon: ReceiptIcon,
        title: String,
        subtitle: String,
        showCheckbox: Bool = false
    ) -> some View {
        HStack(spacing: 8) {
            // Icon container
            HStack {
                ZStack {
                    if showCheckbox {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 15, height: 15)
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .stroke(Color(red: 0.01, green: 0.65, blue: 0.57), lineWidth: 0.75)
                            )
                    } else {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 20, height: 20)
                            .overlay(
                                Image(systemName: icon.systemName)
                                    .foregroundColor(.white)
                                    .font(.system(size: 10, weight: .medium))
                            )
                    }
                }
                .frame(width: 20, height: 20)
            }
            .padding(8)
            .frame(width: 36, height: 36)
            .cornerRadius(100)

            // Text content
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.custom("SF Pro", size: 16))
                    .lineSpacing(21)
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.custom("SF Pro", size: 12))
                    .lineSpacing(16)
                    .foregroundColor(Color(red: 0.76, green: 0.77, blue: 0.77))
            }
            .frame(height: 42)

            Spacer()
        }
    }

    private func divider() -> some View {
        Rectangle()
            .foregroundColor(.clear)
            .frame(height: 0.5)
            .overlay(
                Rectangle()
                    .stroke(Color(red: 0.18, green: 0.18, blue: 0.18), lineWidth: 0.50)
            )
    }
}

enum ReceiptIcon {
    case calendar
    case building
    case money
    case category
    case documentType
    case paymentMethod
    case description

    var systemName: String {
        switch self {
        case .calendar:
            return "calendar"
        case .building:
            return "building.2"
        case .money:
            return "banknote"
        case .category:
            return "tag"
        case .documentType:
            return "doc.text"
        case .paymentMethod:
            return "creditcard"
        case .description:
            return "text.alignleft"
        }
    }
}

struct ReceiptData {
    let date: String
    let supplier: String
    let amount: String
    let currency: String
    let category: String
    let documentType: String
    let paymentMethod: String
    let description: String
    let imageData: Data?

    var formattedAmount: String {
        if currency == "NOT_EXTRACTED" || currency.isEmpty {
            return "\(amount) CZK"
        }
        return "\(amount) \(currency)"
    }

    init(from json: [String: Any], imageData: Data? = nil) {
        self.date = json["date"] as? String ?? "N/A"
        self.supplier = json["supplier"] as? String ?? "N/A"
        self.amount = json["amount_with_vat"] as? String ?? json["amount"] as? String ?? "0.00"
        self.currency = json["currency"] as? String ?? "CZK"
        self.documentType = json["document_type"] as? String ?? "Receipt"
        self.paymentMethod = json["payment_method"] as? String ?? "N/A"
        self.description = json["description"] as? String ?? ""
        self.imageData = imageData

        // Map document type to Czech category
        switch documentType.lowercased() {
        case "meals and entertainment", "meal", "restaurant":
            self.category = "Občerstvení"
        case "transport", "taxi", "fuel":
            self.category = "Doprava"
        case "office supplies", "supplies":
            self.category = "Kancelářské potřeby"
        case "accommodation", "hotel":
            self.category = "Ubytování"
        default:
            self.category = "Ostatní"
        }
    }
}

#Preview {
    ReceiptCardView(receiptData: ReceiptData(from: [
        "document_type": "Meals and Entertainment",
        "date": "2025-05-23",
        "supplier": "NOČNÝBAR DÍRA",
        "amount_with_vat": "1960",
        "currency": "NOT_EXTRACTED",
        "payment_method": "NOT_EXTRACTED",
        "description": "OBČERSTVen (refreshment-related expense)"
    ]))
    .background(Color.black)
}
